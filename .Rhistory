# Load required libraries
library(dada2)
library(Biostrings)
# === INPUT FILES (all should be in your working directory) ===
otu_fasta <- "cluster.cluster.otus.fa"
silva_trainset <- "silva_nr99_v138.2_toGenus_trainset.fa.gz"
# === STEP 1: READ OTU FASTA ===
cat("Reading OTU fasta...\n")
otus <- readDNAStringSet(otu_fasta)
otu_ids <- names(otus)  # store original names
n_otus <- length(otus)
cat("Total OTUs read:", n_otus, "\n")
# === STEP 2: SPLIT INTO CHUNKS FOR PROGRESS FEEDBACK ===
chunk_size <- 10000  # Adjust if needed
n_chunks <- ceiling(n_otus / chunk_size)
cat("Splitting into", n_chunks, "chunks of", chunk_size, "OTUs each.\n")
# === STEP 3: ASSIGN TAXONOMY CHUNK BY CHUNK ===
all_taxa <- list()
for (i in 1:n_chunks) {
cat("\nProcessing chunk", i, "of", n_chunks, "...\n")
idx_start <- (i - 1) * chunk_size + 1
idx_end <- min(i * chunk_size, n_otus)
idx_range <- idx_start:idx_end
chunk <- otus[idx_range]
chunk_ids <- otu_ids[idx_range]
# Run genus-level classification using sequences directly
taxa_chunk <- assignTaxonomy(
seqs = as.character(chunk),
refFasta = silva_trainset,
multithread = TRUE
)
# Set row names to OTU IDs instead of sequences
rownames(taxa_chunk) <- chunk_ids
all_taxa[[i]] <- taxa_chunk
}
# === STEP 4: COMBINE ALL CHUNKS ===
cat("\nCombining all taxonomy results...\n")
taxa_full <- do.call(rbind, all_taxa)
# === STEP 5: SAVE RESULTS ===
output_file <- "OTU_taxonomy_assignment.csv"
write.csv(taxa_full, file = output_file, quote = FALSE)
cat("\nTaxonomy assignment complete. Results saved to:", output_file, "\n")
